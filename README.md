# 🛒 Enhanced E-Commerce System

## Fawry Rise Journey - Full Stack Development Internship Challenge

A comprehensive, feature-rich e-commerce system that goes beyond the basic requirements to demonstrate advanced software engineering principles and business logic implementation.

## 🎯 Project Overview

This project implements a sophisticated e-commerce system with the following capabilities:

### Core Requirements (Fully Implemented)

- **Product Management**: Products with name, price, and quantity
- **Expiration Handling**: Some products expire (Cheese, Biscuits), others don't (TV, Mobile cards)
- **Shipping Logic**: Some products require shipping with weight calculation
- **Cart Operations**: Add products with quantity validation
- **Checkout Process**: Complete validation and payment processing
- **Error Handling**: Comprehensive validation for all edge cases
- **Shipping Service**: Integration with weight-based shipping calculations

### 🚀 Advanced Features (Beyond Requirements)

- **Smart Discount System**: Multiple discount types (bulk, welcome, VIP, multi-item)
- **Tax Calculation**: Category-based tax rates (food 5%, electronics 10%, digital 12%, general 8%)
- **Professional UI**: Color-coded console output with symbols and formatting
- **Enhanced Error Handling**: Custom exceptions with detailed error messages
- **Weight-Based Shipping**: Dynamic shipping costs based on package weight
- **Free Shipping**: Automatic free shipping for orders over $500
- **Cart Management**: Advanced cart operations (add, remove, display, clear)
- **Comprehensive Testing**: Extensive test suite covering all scenarios

## 🏗️ Architecture & Design Patterns

### SOLID Principles Implementation

- **Single Responsibility**: Each class has a focused purpose
- **Open/Closed**: Extensible design through inheritance and interfaces
- **Liskov Substitution**: Proper inheritance hierarchy
- **Interface Segregation**: Clean interface design (Shippable)
- **Dependency Inversion**: Service-based architecture

### Design Patterns Used

- **Strategy Pattern**: Different tax rates and discount calculations
- **Template Method**: Product hierarchy with customizable behavior
- **Factory Pattern**: Product creation with different types
- **Observer Pattern**: Console formatting and user feedback

## 📁 Project Structure

```
fawry-task/
├── Product.java                    # Base product class
├── ExpirableProduct.java          # Products that can expire
├── ShippableProduct.java          # Products that can be shipped
├── ShippableExpirableProduct.java # Products that expire and ship
├── Shippable.java                 # Interface for shippable items
├── Customer.java                  # Customer management
├── Cart.java                      # Shopping cart with validation
├── CheckoutService.java           # Enhanced checkout with discounts/taxes
├── ShippingService.java           # Advanced shipping calculations
├── Main.java                      # Basic demonstration
├── TestSuite.java                 # Comprehensive test suite
├── exceptions/                    # Custom exception classes
│   ├── ECommerceException.java
│   ├── InsufficientStockException.java
│   ├── InsufficientBalanceException.java
│   ├── ProductExpiredException.java
│   └── EmptyCartException.java
└── utils/
    └── ConsoleFormatter.java      # Professional console formatting
```

## 🚀 Quick Start Guide

### Prerequisites

- **Java 8 or higher** installed on your system
- **Command line access** (Terminal on Mac/Linux, Command Prompt/PowerShell on Windows)
- **Text editor or IDE** (optional, for viewing code)

### 📥 Setup Instructions

1. **Download/Clone the project** to your local machine
2. **Navigate to the project directory**:
   ```bash
   cd fawry-task
   ```
3. **Verify Java installation**:
   ```bash
   java -version
   javac -version
   ```

## 🎮 How to Run and Test

### 🎯 Option 1: Basic Demo (PDF Example)

**Perfect for quick verification of core requirements**

```bash
# Compile all files
javac *.java exceptions/*.java utils/*.java

# Run the basic demo
java Main
```

**What you'll see:**

- Basic scenario matching the PDF example
- Cheese + Biscuits purchase
- Error handling demonstrations
- Free shipping example

---

### 🧪 Option 2: Comprehensive Test Suite

**Best for seeing all advanced features**

```bash
# Compile all files (if not done already)
javac *.java exceptions/*.java utils/*.java

# Run comprehensive tests
java TestSuite
```

**What you'll see:**

- All basic requirements demonstrated
- Advanced features (discounts, taxes, professional UI)
- Error handling for all edge cases
- Shipping calculations and free shipping
- Cart management operations

---

### 🎊 Option 3: Final Demo (Complete Showcase)

**Ultimate demonstration of all capabilities**

```bash
# Compile all files (if not done already)
javac *.java exceptions/*.java utils/*.java

# Run the final demo
java FinalDemo
```

**What you'll see:**

- 5 different scenarios showcasing various features
- VIP customer with multiple discounts
- Mixed cart (physical + digital products)
- Complete error handling demonstration
- Professional console output with colors and symbols

---

## 🔍 Testing Different Scenarios

### Scenario 1: Basic Requirements ✅

```bash
java Main
```

**Tests:** Core functionality, basic checkout, shipping integration

### Scenario 2: Advanced Features 🚀

```bash
java TestSuite
```

**Tests:** Discounts, taxes, professional UI, weight-based shipping

### Scenario 3: Error Handling 🛡️

Both `TestSuite` and `FinalDemo` include comprehensive error testing:

- Insufficient stock validation
- Invalid quantity handling
- Expired product detection
- Empty cart validation
- Insufficient balance checking

### Scenario 4: Edge Cases 🎯

**Manual testing examples:**

```bash
# Compile first
javac *.java exceptions/*.java utils/*.java

# Then try these individual tests by modifying Main.java:
```

**Test Large Orders (VIP Discounts):**

```java
// In Main.java, try:
cart.add(tv, 2);  // $10,000 order - triggers VIP discount
```

**Test Free Shipping:**

```java
// Orders over $500 get free shipping
cart.add(tv, 1);  // $5000 - qualifies for free shipping
```

**Test Multiple Items (Multi-item Discount):**

```java
// 5+ items get additional discount
cart.add(cheese, 2);
cart.add(biscuits, 2);
cart.add(scratchCard, 2);  // 6 items total
```

---

## 📊 Expected Output Examples

### Successful Checkout

```
Added 2 x Cheese to cart.
Added 1 x Biscuits to cart.

💰 Applying Discounts:
────────────────────────────────────────
Bulk Order (10% off): -$35.00
Welcome Discount: -$25.00
────────────────────────────────────────
Total Savings:                $60.00

📊 Tax Calculation:
────────────────────────────────────────
Cheese (5.0%)                 $10.00
Biscuits (5.0%)                $7.50
────────────────────────────────────────
Total Tax:                    $17.50

** Shipment Notice **
═════════════════════════════════════════════
2  x Cheese                     400g
1  x Biscuits                   700g
─────────────────────────────────────────────
Total package weight:          1.1kg
Shipping fee:                 $26.00
═════════════════════════════════════════════

** Checkout Receipt **
ℹ Customer: Ahmed
═════════════════════════════════════════════
2  x Cheese                  $200.00
1  x Biscuits                $150.00
─────────────────────────────────────────────
Subtotal                     $350.00
Discounts                    -$60.00
After Discounts              $290.00
Shipping                      $26.00
Tax                           $17.50
─────────────────────────────────────────────
Total Amount                 $333.50
═════════════════════════════════════════════
ℹ Ahmed's balance after payment: $666.50
```

### ❌ Error Handling Examples

```
❌ [INSUFFICIENT_STOCK] Insufficient stock for TV. Requested: 10, Available: 5
❌ Quantity must be positive
❌ Product cannot be null
❌ Cannot checkout with an empty cart
❌ Insufficient balance. Required: $5000.00, Available: $50.00
❌ Product Expired Milk has expired on 2025-06-30
```

---

## 🧪 Manual Testing Checklist

### Core Requirements Testing ✅

- [ ] Products with name, price, quantity work
- [ ] Expirable products (Cheese, Biscuits) vs non-expirable (TV, Cards)
- [ ] Shippable products (Cheese, TV) vs non-shippable (Cards)
- [ ] Weight information for shippable items
- [ ] Cart operations (add with quantity validation)
- [ ] Checkout process with all validations
- [ ] Shipping service integration

### Advanced Features Testing 🚀

- [ ] Bulk discount (10% off orders over $300)
- [ ] Welcome discount ($25 off for new customers)
- [ ] VIP discount (15% off orders over $1000)
- [ ] Multi-item discount (5% off for 5+ items)
- [ ] Tax calculation by category
- [ ] Weight-based shipping costs
- [ ] Free shipping for orders over $500
- [ ] Professional console formatting

### Error Handling Testing 🛡️

- [ ] Insufficient stock error
- [ ] Invalid quantity (zero/negative)
- [ ] Null product handling
- [ ] Empty cart checkout
- [ ] Insufficient customer balance
- [ ] Expired product detection

---

## 🎨 Visual Features to Notice

### Color-Coded Output 🌈

- **Green (✅)**: Success messages, applied discounts
- **Red (❌)**: Error messages, warnings
- **Yellow (⚠️)**: Warnings, tax information
- **Cyan (ℹ️)**: Information messages
- **Blue**: Headers and titles

### Professional Formatting 📋

- **Symbols**: ❌ ⚠️ ℹ️ 🛒 📦 💰 📊 🎉 👑
- **Tables**: Aligned columns with separators
- **Currency**: Proper formatting ($1,234.56)
- **Weight**: Smart units (400g vs 1.1kg)

---

## 🔧 Troubleshooting

### Common Issues and Solutions

**Issue**: `javac: command not found`

```bash
# Solution: Install Java JDK
# Windows: Download from Oracle or use chocolatey
choco install openjdk

# Mac: Use Homebrew
brew install openjdk

# Linux: Use package manager
sudo apt install default-jdk  # Ubuntu/Debian
sudo yum install java-devel   # CentOS/RHEL
```

**Issue**: `Exception in thread "main" java.lang.NoClassDefFoundError`

```bash
# Solution: Make sure you're in the correct directory and compiled all files
cd fawry-task
javac *.java exceptions/*.java utils/*.java
java Main  # or TestSuite or FinalDemo
```

**Issue**: Colors not showing in Windows Command Prompt

```bash
# Solution: Use Windows Terminal or PowerShell for better color support
# Or run: java Main > output.txt to save output to file
```

**Issue**: Compilation errors

```bash
# Solution: Ensure Java 8+ and compile all packages
javac -version  # Check Java version
javac *.java exceptions/*.java utils/*.java  # Compile all files
```

---

## 📈 Performance Testing

### Load Testing Ideas

```java
// Test with large quantities
cart.add(product, 1000);

// Test with many different products
for(int i = 0; i < 100; i++) {
    cart.add(new Product("Item" + i, 10.0, 1000), 1);
}

// Test edge case values
cart.add(new Product("Expensive", 999999.99, 1), 1);
```

### Memory Testing

```bash
# Run with memory monitoring
java -Xmx64m Main  # Limit memory to test efficiency
```

---

## 🎯 What to Look For

### Success Indicators

1. **Clean compilation** with no errors
2. **Professional output** with colors and formatting
3. **Accurate calculations** for discounts, taxes, shipping
4. **Proper error handling** for all invalid inputs
5. **Realistic business logic** that makes sense

### 🚀 Advanced Features to Notice

1. **Smart discount stacking** (multiple discounts applied correctly)
2. **Category-based tax rates** (food 5%, electronics 10%, etc.)
3. **Weight-based shipping** with free shipping threshold
4. **Professional UI** with symbols and colors
5. **Comprehensive validation** at every step

---

## 📞 Support

If you encounter any issues:

1. **Check Java version**: `java -version` (needs 8+)
2. **Verify compilation**: All `.java` files should compile without errors
3. **Check directory**: Make sure you're in the `fawry-task` folder
4. **Review output**: Look for specific error messages

**Happy Testing! 🎉**

## 💡 Key Features Demonstration

### 1. Basic Scenario (PDF Example)

```java
// Create products
Product cheese = new ShippableExpirableProduct("Cheese", 100, 10, 0.2, LocalDate.now().plusDays(30));
Product biscuits = new ShippableExpirableProduct("Biscuits", 150, 15, 0.7, LocalDate.now().plusMonths(6));

// Create customer and cart
Customer customer = new Customer("Ahmed", 1000.0);
Cart cart = new Cart();

// Add items and checkout
cart.add(cheese, 2);
cart.add(biscuits, 1);
CheckoutService.checkout(customer, cart);
```

**Output:**

```
Added 2 x Cheese to cart.
Added 1 x Biscuits to cart.

💰 Applying Discounts:
────────────────────────────────────────
Bulk Order (10% off): -$35.00
Welcome Discount: -$25.00
────────────────────────────────────────
Total Savings:                $60.00

📊 Tax Calculation:
────────────────────────────────────────
Cheese (5.0%)                 $10.00
Biscuits (5.0%)                $7.50
────────────────────────────────────────
Total Tax:                    $17.50

** Shipment Notice **
═════════════════════════════════════════════
2  x Cheese                     400g
1  x Biscuits                   700g
─────────────────────────────────────────────
Total package weight:          1.1kg
Shipping fee:                 $26.00
═════════════════════════════════════════════

** Checkout Receipt **
═════════════════════════════════════════════
2  x Cheese                  $200.00
1  x Biscuits                $150.00
─────────────────────────────────────────────
Subtotal                     $350.00
Discounts                    -$60.00
After Discounts              $290.00
Shipping                      $26.00
Tax                           $17.50
─────────────────────────────────────────────
Total Amount                 $333.50
═════════════════════════════════════════════
ℹ Customer balance after payment: $666.50
```

### 2. Advanced Features

#### Smart Discount System

- **Bulk Orders**: 10% off orders over $300
- **Welcome Discount**: $25 off for new customers
- **VIP Discount**: 15% off orders over $1000
- **Multi-Item**: 5% off when buying 5+ items

#### Dynamic Tax Calculation

- **Food Items**: 5% tax rate
- **Electronics**: 10% tax rate
- **Digital Products**: 12% tax rate
- **General Items**: 8% tax rate

#### Intelligent Shipping

- **Weight-Based**: $15 base + $10 per kg
- **Free Shipping**: Orders over $500
- **Mixed Carts**: Only shippable items incur shipping

### 3. Error Handling Examples

```java
// Insufficient stock
cart.add(tv, 10); // Only 5 available
// Output: ❌ [INSUFFICIENT_STOCK] Insufficient stock for TV. Requested: 10, Available: 5

// Invalid quantity
cart.add(product, 0);
// Output: ❌ Quantity must be positive

// Insufficient balance
CheckoutService.checkout(poorCustomer, expensiveCart);
// Output: ❌ Insufficient balance. Required: $5000.00, Available: $50.00

// Expired product
CheckoutService.checkout(customer, cartWithExpiredMilk);
// Output: ❌ Product Expired Milk has expired on 2025-06-30
```

## 🧪 Testing Coverage

The system includes comprehensive testing for:

- All basic requirements
- Error handling and edge cases
- Discount calculations
- Tax calculations
- Shipping logic
- Cart operations
- Product expiration
- Stock management
- Balance validation

## 🎨 User Experience Features

### Professional Console Output

- **Color-coded messages**: Success (green), errors (red), warnings (yellow), info (cyan)
- **Symbols and icons**: ❌ ⚠ ℹ 🛒 📦 💰 📊
- **Formatted tables**: Aligned columns and separators
- **Progress indicators**: Clear feedback for all operations

### Enhanced Feedback

- **Detailed error messages**: Specific information about what went wrong
- **Success confirmations**: Clear indication of successful operations
- **Summary information**: Comprehensive receipts and reports

## 🔧 Technical Highlights

### Exception Handling

- Custom exception hierarchy with specific error codes
- Graceful error recovery and user-friendly messages
- Comprehensive validation at all input points

### Performance Considerations

- Efficient algorithms for discount and tax calculations
- Optimized data structures for cart operations
- Minimal memory footprint with proper object lifecycle

### Code Quality

- Comprehensive JavaDoc documentation
- Consistent naming conventions
- Clean, readable code structure
- Proper separation of concerns

## 🏆 What Makes This Solution Stand Out

1. **Goes Beyond Requirements**: Implements advanced business logic not specified
2. **Professional Quality**: Production-ready code with proper error handling
3. **User Experience**: Beautiful, intuitive console interface
4. **Extensible Design**: Easy to add new features and product types
5. **Comprehensive Testing**: Thorough validation of all functionality
6. **Real-World Features**: Discounts, taxes, and shipping like actual e-commerce
7. **Clean Architecture**: SOLID principles and design patterns
8. **Documentation**: Complete documentation and examples

## 🚀 Future Enhancements

The system is designed to easily accommodate:

- Database integration
- Web interface
- Payment gateway integration
- Inventory management system
- Order history and tracking
- Customer loyalty programs
- Product recommendations
- Multi-currency support

---

**Developed by**: Ahmed Taha  
**Challenge**: Fawry Rise Journey - Full Stack Development Internship  
**Date**: July 2025
