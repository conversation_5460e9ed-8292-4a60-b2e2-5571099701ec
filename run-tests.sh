#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================"
echo -e " Fawry E-Commerce System Test Runner"
echo -e "========================================${NC}"
echo

# Check if Java is installed
if ! command -v java &> /dev/null; then
    echo -e "${RED}ERROR: Java is not installed or not in PATH${NC}"
    echo "Please install Java 8 or higher and try again"
    exit 1
fi

if ! command -v javac &> /dev/null; then
    echo -e "${RED}ERROR: Java compiler (javac) is not installed or not in PATH${NC}"
    echo "Please install Java JDK 8 or higher and try again"
    exit 1
fi

echo -e "${YELLOW}Compiling Java files...${NC}"
javac *.java exceptions/*.java utils/*.java
if [ $? -ne 0 ]; then
    echo -e "${RED}ERROR: Compilation failed${NC}"
    exit 1
fi

echo -e "${GREEN}Compilation successful!${NC}"
echo

while true; do
    echo -e "${CYAN}Choose a test to run:${NC}"
    echo "1. Basic Demo"
    echo "2. Comprehensive Test"
    echo "3. Complete Showcase"
    echo "4. Exit"
    echo
    read -p "Enter your choice (1-4): " choice

    case $choice in
        1)
            echo
            echo -e "${BLUE}========================================"
            echo -e " Running Basic Demo"
            echo -e "========================================${NC}"
            echo
            java Main
            ;;
        2)
            echo
            echo -e "${BLUE}========================================"
            echo -e " Running Comprehensive Test"
            echo -e "========================================${NC}"
            echo
            java TestSuite
            ;;
        3)
            echo
            echo -e "${BLUE}========================================"
            echo -e " Running Complete Showcase"
            echo -e "========================================${NC}"
            echo
            java FinalDemo
            ;;
        4)
            echo
            echo -e "${GREEN}Thank you!${NC}"
            echo
            exit 0
            ;;
        *)
            echo -e "${RED}Invalid choice. Please try again.${NC}"
            continue
            ;;
    esac

    echo
    echo -e "${BLUE}========================================"
    echo -e "Test completed!"
    echo -e "========================================${NC}"
    echo
    read -p "Run another test? (y/n): " again
    case $again in
        [Yy]* ) continue;;
        [Nn]* ) break;;
        * ) echo "Please answer yes or no.";;
    esac
done

echo
echo -e "${GREEN}Thank you!${NC}"
echo
